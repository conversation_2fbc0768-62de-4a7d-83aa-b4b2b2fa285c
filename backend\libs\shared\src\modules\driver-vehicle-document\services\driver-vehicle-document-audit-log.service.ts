import { Injectable, Logger } from '@nestjs/common';
import { RabbitMQEventPublisher } from '../../../event-emitter/publishers/rabbitmq-event.publisher';
import {
  AuditAction,
  AuditResource,
} from '../../audit-log/enums/audit-log.enum';
import { AuditLogData } from '@shared/shared/event-emitter/interface/auditLog.interface';
import { DriverVehicleAuditLogService } from '../../driver-vehicle/services/driver-vehicle-audit-log.service';
import { DriverVehicleDocument } from '@shared/shared/repositories/models/driverVehicleDocument.model';

@Injectable()
export class DriverVehicleDocumentAuditLogService {
  private readonly logger = new Logger(DriverVehicleAuditLogService.name);

  constructor(
    private readonly rabbitmqEventPublisher: RabbitMQEventPublisher,
  ) {}

  /**
   * Log driver vehicle document verification
   */
  async logDocumentVerification(
    userProfileId: string,
    vehicleId: string,
    vehicleNumber: string,
    driverId?: string,
  ): Promise<void> {
    const auditData: AuditLogData = {
      userProfileId,
      action: AuditAction.UPDATE,
      resource: AuditResource.DRIVER_VEHICLE,
      resourceId: vehicleId,
      description: `Vehicle documents updated for vehicle number: ${vehicleNumber}`,
      ...(driverId && { driverId }),
    };

    this.logger.log(
      `Logging driver vehicle document verification: ${JSON.stringify(auditData)}`,
    );
    await this.rabbitmqEventPublisher.publishAuditLog(auditData);
  }

  /**
   * Log NOC document upload
   */
  async logNocUpload(
    userProfileId: string,
    vehicleId: string,
    vehicleNumber: string,
    driverId?: string,
  ): Promise<void> {
    const auditData: AuditLogData = {
      userProfileId,
      action: AuditAction.UPDATE,
      resource: AuditResource.DRIVER_VEHICLE,
      resourceId: vehicleId,
      description: `Noc updated for vehicle number: ${vehicleNumber}`,
      ...(driverId && { driverId }),
    };

    this.logger.log(
      `Logging driver vehicle NOC upload: ${JSON.stringify(auditData)}`,
    );
    await this.rabbitmqEventPublisher.publishAuditLog(auditData);
  }

  /**
   * Log document approval
   */
  async logDocumentApproval(
    userProfileId: string,
    document: DriverVehicleDocument,
  ): Promise<void> {
    const auditData: AuditLogData = {
      userProfileId,
      action: AuditAction.UPDATE,
      resource: AuditResource.DRIVER_VEHICLE,
      resourceId: document.driverVehicleId,
      description: `Document ${document.vehicleDocument?.name} approved for vehicle number: ${document.driverVehicle?.vehicleNumber}`,
      ...(document.driverVehicle?.userProfileId && {
        driverId: document.driverVehicle.userProfileId,
      }),
    };

    this.logger.log(
      `Logging driver vehicle document approval: ${JSON.stringify(auditData)}`,
    );
    await this.rabbitmqEventPublisher.publishAuditLog(auditData);
  }

  /**
   * Log document rejection
   */
  async logDocumentRejection(
    userProfileId: string,
    document: DriverVehicleDocument,
  ): Promise<void> {
    const auditData: AuditLogData = {
      userProfileId,
      action: AuditAction.UPDATE,
      resource: AuditResource.DRIVER_VEHICLE,
      resourceId: document.driverVehicleId,
      description: `Document ${document.vehicleDocument.name} rejected for vehicle number: ${document.driverVehicle.vehicleNumber}`,
      ...(document.driverVehicle.userProfileId && {
        driverId: document.driverVehicle.userProfileId,
      }),
    };

    this.logger.log(
      `Logging driver vehicle document rejection: ${JSON.stringify(auditData)}`,
    );
    await this.rabbitmqEventPublisher.publishAuditLog(auditData);
  }
}
