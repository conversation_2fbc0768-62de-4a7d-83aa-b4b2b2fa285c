import { Modu<PERSON> } from '@nestjs/common';
import { AdminDriverController } from './driver.controller';
import { UserProfileModule } from '@shared/shared/modules/user-profile/user-profile.module';
import { AuthModule } from '@shared/shared/modules/auth/auth.module';
import { LocationIngestorModule } from '@shared/shared/modules/location/location-injestor.module';
import { RealtimeModule } from '../../realtime/realtime.module';
import { CaslModule } from '@shared/shared/casl/casl.module';

@Module({
  imports: [
    AuthModule,
    UserProfileModule,
    LocationIngestorModule,
    RealtimeModule,
    CaslModule,
  ],
  controllers: [AdminDriverController],
  providers: [],
  exports: [],
})
export class ApiDriverModule {}
