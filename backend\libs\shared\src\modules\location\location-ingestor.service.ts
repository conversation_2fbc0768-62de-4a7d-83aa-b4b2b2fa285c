import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import {
  CellDriverRepository,
  DriverMetadata,
  DriverMetadataRepository,
  H3UtilityService,
  PrismaService,
} from '@shared/shared';
import { DriverLocationReceivedDto } from '@shared/shared/event-emitter/dto/driver-location-received.dto';
import { RealtimeGateway } from 'apps/api/src/v1/realtime/realtime.gateway';
import { RealtimeService } from 'apps/api/src/v1/realtime/realtime.service';

// Interface for location update callback
export interface ILocationUpdateCallback {
  processLocationUpdateFromIngestor(locationData: {
    driverId: string;
    lat: number;
    lng: number;
    timestamp: string;
    city?: string;
    status: string;
    rideId?: string;
    bearing?: number;
    speed?: number;
    heading?: number;
  }): Promise<void>;
}

/**
 * Service responsible for processing driver location updates
 * and updating Redis stores for efficient location-based queries
 */
@Injectable()
export class LocationIngestorService {
  private readonly logger = new Logger(LocationIngestorService.name);

  constructor(
    private readonly cellDriverRepository: CellDriverRepository,
    private readonly driverMetadataRepository: DriverMetadataRepository,
    @Inject(forwardRef(() => H3UtilityService))
    private readonly h3UtilityService: H3UtilityService,
    private readonly prisma: PrismaService,

    @Inject(forwardRef(() => RealtimeService))
    private readonly realtimeService: RealtimeService,
    @Inject(forwardRef(() => RealtimeGateway))
    private readonly realtimeGateway: RealtimeGateway,
  ) {}

  /**
   * Get drivers in a specific H3 cell
   */
  async getDriversInCell(
    h3Index: string,
    limit: number = 50,
  ): Promise<Array<{ driverId: string; metadata: DriverMetadata | null }>> {
    try {
      const driversWithTimestamps =
        await this.cellDriverRepository.findRecentDriverIds(h3Index, limit);
      this.logger.log(
        `Found ${driversWithTimestamps.length} drivers in cell ${h3Index}`,
      );
      if (driversWithTimestamps.length === 0) {
        return [];
      }

      const driverIds = driversWithTimestamps.map((d) => d.driverId);
      const metadataMap =
        await this.driverMetadataRepository.getDriversMetadata(driverIds);
      return driversWithTimestamps.map(({ driverId }) => ({
        driverId,
        metadata: metadataMap[driverId],
      }));
    } catch (error) {
      this.logger.error(`Failed to get drivers in cell ${h3Index}`, error);
      throw error;
    }
  }

  /**
   * Find all nearby drivers within a specified distance from an H3 cell
   * @param h3Cell - Center H3 cell
   * @param distance - Search distance in kilometers (used to determine H3 ring size)
   * @param city - City to search in
   * @param limit - Maximum number of drivers to return
   * @returns Array of drivers with metadata
   */
  async findAllNearbyDrivers(
    h3Cell: string,
    distance: number,
    limit: number = 50,
  ): Promise<Array<{ driverId: string; metadata: DriverMetadata | null }>> {
    try {
      this.logger.log(
        `Finding nearby drivers within ${distance} H3 cells of H3 cell ${h3Cell}`,
      );

      // Calculate H3 ring size based on distance
      // Approximate: 1km ≈ H3 ring size 1-2 at resolution 8
      // const ringSize = Math.max(1, Math.ceil(distance / 2));

      // Get neighboring H3 cells within the ring
      const neighboringCells = this.h3UtilityService.getNeighboringCells(
        h3Cell,
        distance,
      );

      // Include the center cell
      const allCells = [h3Cell, ...neighboringCells];

      this.logger.debug(
        `Searching in ${allCells.length} H3 cells (ring size: ${distance})`,
      );

      // Get drivers from all cells
      const allDrivers: Array<{
        driverId: string;
        metadata: DriverMetadata | null;
      }> = [];

      for (const cell of allCells) {
        try {
          const cellDrivers = await this.getDriversInCell(cell, limit);
          allDrivers.push(...cellDrivers);
        } catch (error) {
          this.logger.warn(`Failed to get drivers from cell ${cell}:`, error);
          // Continue with other cells
        }
      }

      // Remove duplicates and limit results
      const uniqueDrivers = allDrivers.reduce(
        (acc, driver) => {
          if (!acc.find((d) => d.driverId === driver.driverId)) {
            acc.push(driver);
          }
          return acc;
        },
        [] as Array<{ driverId: string; metadata: DriverMetadata | null }>,
      );

      const limitedDrivers = uniqueDrivers.slice(0, limit);

      this.logger.log(`Found ${limitedDrivers.length} unique nearby drivers`);

      return limitedDrivers;
    } catch (error) {
      this.logger.error(
        `Failed to find nearby drivers for H3 cell ${h3Cell}`,
        error,
      );
      throw error;
    }
  }
  //for testing only
  async processDriverLocationUpdateDev(
    data: DriverLocationReceivedDto,
  ): Promise<void> {
    try {
      this.logger.log(`Processing location update for driver ${data.driverId}`);
      await this.realtimeService.processLocationUpdate(data);
      if (data.rideId) {
        this.logger.log(
          `Creating driver location broadcast for driver ${data.driverId} and ride ${data.rideId}`,
        );
        await this.realtimeGateway.broadcastDriverLocationToRide(
          data.rideId,
          data,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to process location update for driver ${data.driverId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : '',
      );
      throw error;
    }
  }

  /**
   * Get drivers within map bounds for radar map view
   * @param bounds - Bounding box with northeast and southwest coordinates
   * @returns Array of drivers with locations and metadata
   */
  async getRadarMapDrivers(bounds: any): Promise<any> {
    try {
      this.logger.log(
        `Fetching drivers for map bounds: NE(${bounds.northeast.lat}, ${bounds.northeast.lng}), SW(${bounds.southwest.lat}, ${bounds.southwest.lng})`,
      );

      // Step 1: Convert bounding box to H3 cells at resolution 8
      const h3Cells = this.convertBoundsToH3Cells(bounds);
      this.logger.log(`Found ${h3Cells.length} H3 cells in bounds`);

      if (h3Cells.length === 0) {
        return { drivers: [], total: 0 };
      }
      return this.fetchRadarMapDriversWithH3Cells(h3Cells);
    } catch (error) {
      this.logger.error('Failed to convert bounds to H3 cells:', error);
      throw error;
    }
  }

async fetchRadarMapDriversWithH3Cells(h3Cells: any): Promise<any> {

  try {
    this.logger.log(`Fetching drivers for ${h3Cells.length} H3 cells`);
      // Step 2: Get driver IDs from all H3 cells
      const driversInCells =
        await this.cellDriverRepository.findRecentDriversInCells(
          h3Cells,
          100, // MAX_DRIVERS_PER_CELL
        );

      if (driversInCells.length === 0) {
        return { drivers: [], total: 0 };
      }

      const driverIds = driversInCells.map((d) => d.driverId);
      this.logger.log(`Found ${driverIds.length} drivers in H3 cells`);

      // Step 3: Get driver metadata from Redis
      const metadataMap =
        await this.driverMetadataRepository.getDriversMetadata(driverIds);

      // Step 4: Get ONLY essential driver data and city product info from database
      // Optimized query: only fetch driver ID and primary city product with product name/icon
      const drivers = await this.prisma.userProfile.findMany({
        where: {
          id: { in: driverIds },
        },
        select: {
          id: true,
          driverVehicles: {
            where: {
              isPrimary: true,
              deletedAt: null,
            },
            select: {
              vehicleTypeId: true,
              driverCityProducts: {
                select: {
                  cityProductId: true,
                  cityProduct: {
                    select: {
                      id: true,
                      product: {
                        select: {
                          name: true,
                          icon: true,
                        },
                      },
                    },
                  },
                },
              },
              vehicleType: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
        },
      });

      // Step 5: Build minimal response with only essential data
      const skippedDrivers = {
        noMetadata: [] as string[],
        noPrimaryProduct: [] as string[],
      };

      const radarDrivers = drivers
        .map((driver) => {
          const metadata = metadataMap[driver.id];

          // Skip drivers without location metadata
          if (!metadata) {
            skippedDrivers.noMetadata.push(driver.id);
            this.logger.debug(
              `Skipping driver ${driver.id}: No location metadata in Redis`,
            );
            return null;
          }

          // Get primary city product (should be only one due to take: 1)
          const primaryCityProduct = driver.driverVehicles[0];
          if (!primaryCityProduct) {
            skippedDrivers.noPrimaryProduct.push(driver.id);
            this.logger.debug(
              `Skipping driver ${driver.id}: No primary city product assigned`,
            );
            return null;
          }

          return {
            id: driver.id,
            ...metadata,
            vehicleTypeId: primaryCityProduct.vehicleTypeId,
            vehicleTypeName: primaryCityProduct.vehicleType.name,
            vehicleTypeImage: primaryCityProduct.vehicleType.image,
            productName:
              primaryCityProduct.driverCityProducts[0].cityProduct.product.name,
            productIcon:
              primaryCityProduct.driverCityProducts[0].cityProduct.product.icon,
          };
        })
        .filter((driver) => driver !== null);

      this.logger.log(
        `Radar map drivers summary: Total fetched=${driverIds.length}, ` +
          `Returned=${radarDrivers.length}, ` +
          `Skipped (no metadata)=${skippedDrivers.noMetadata.length}, ` +
          `Skipped (no primary product)=${skippedDrivers.noPrimaryProduct.length}`,
      );

      if (skippedDrivers.noMetadata.length > 0) {
        this.logger.warn(
          `Drivers skipped due to missing Redis metadata: ${skippedDrivers.noMetadata.join(', ')}`,
        );
      }

      if (skippedDrivers.noPrimaryProduct.length > 0) {
        this.logger.warn(
          `Drivers skipped due to missing primary city product: ${skippedDrivers.noPrimaryProduct.join(', ')}`,
        );
      }

      return {
        drivers: radarDrivers,
        total: radarDrivers.length,
      };
    } catch (error) {
      this.logger.error('Failed to get drivers in map bounds:', error);
      throw error;
    }
  }

  /**
   * Convert bounding box to H3 cells at resolution 8
   * Creates a polygon from the bounding box and converts to H3 cells
   */
  private convertBoundsToH3Cells(bounds: any): string[] {
    try {
      // Create a polygon from the bounding box
      const polygon = {
        type: 'Polygon' as const,
        coordinates: [
          [
            [bounds.southwest.lng, bounds.southwest.lat], // SW
            [bounds.northeast.lng, bounds.southwest.lat], // SE
            [bounds.northeast.lng, bounds.northeast.lat], // NE
            [bounds.southwest.lng, bounds.northeast.lat], // NW
            [bounds.southwest.lng, bounds.southwest.lat], // Close polygon
          ],
        ],
      };

      // Convert polygon to H3 cells
      const h3Cells = this.h3UtilityService.polygonToH3Indexes(
        polygon,
        8, // H3_RESOLUTION
      );

      return h3Cells;
    } catch (error) {
      this.logger.error('Failed to convert bounds to H3 cells:', error);
      throw error;
    }
  }
}
