import { Injectable, Logger } from '@nestjs/common';
import { RabbitMQEventPublisher } from '../../../event-emitter/publishers/rabbitmq-event.publisher';
import {
  AuditAction,
  AuditResource,
} from '../../audit-log/enums/audit-log.enum';
import { AuditLogData } from '@shared/shared/event-emitter/interface/auditLog.interface';

@Injectable()
export class DriverCityProductAuditLogService {
  private readonly logger = new Logger(DriverCityProductAuditLogService.name);

  constructor(
    private readonly rabbitmqEventPublisher: RabbitMQEventPublisher,
  ) {}

  /**
   * Log adding city products to driver vehicle
   */
  async logAddCityProducts(
    userProfileId: string,
    vehicleId: string,
    vehicleNumber: string,
    driverId?: string,
  ): Promise<void> {
    const auditData: AuditLogData = {
      userProfileId,
      action: AuditAction.UPDATE,
      resource: AuditResource.DRIVER_VEHICLE,
      resourceId: vehicleId,
      description: `City products added to vehicle number: ${vehicleNumber}`,
      ...(driverId && { driverId }),
    };

    this.logger.log(
      `Logging driver city product addition: ${JSON.stringify(auditData)}`,
    );
    await this.rabbitmqEventPublisher.publishAuditLog(auditData);
  }

  /**
   * Log removing city products from driver vehicle
   */
  async logRemoveCityProducts(
    userProfileId: string,
    vehicleId: string,
    vehicleNumber: string,
    driverId?: string,
  ): Promise<void> {
    const auditData: AuditLogData = {
      userProfileId,
      action: AuditAction.UPDATE,
      resource: AuditResource.DRIVER_VEHICLE,
      resourceId: vehicleId,
      description: `City products removed from vehicle number: ${vehicleNumber}`,
      ...(driverId && { driverId }),
    };

    this.logger.log(
      `Logging driver city product removal: ${JSON.stringify(auditData)}`,
    );
    await this.rabbitmqEventPublisher.publishAuditLog(auditData);
  }
}
