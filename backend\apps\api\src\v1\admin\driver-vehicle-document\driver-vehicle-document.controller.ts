import {
  Controller,
  Get,
  Post,
  Param,
  Body,
  Query,
  ParseUUI<PERSON>ipe,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
  BadRequestException,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { DriverVehicleDocumentService } from '@shared/shared/modules/driver-vehicle-document/driver-vehicle-document.service';
import {
  ApproveRejectDocumentDto,
  DocumentAction,
} from './dto/approve-reject-document.dto';
import {
  DriverVehicleDocumentResponseDto,
  DriverVehicleDocumentListResponseDto,
  DocumentActionResponseDto,
} from './dto/driver-vehicle-document-response.dto';
import { ApiErrorResponseDto, ApiResponseDto } from 'apps/api/src/docs/swagger';
import { DriverVehicleDocumentStatus } from '@shared/shared/repositories/models/driverVehicleDocument.model';
import { ApiRequest } from '@shared/shared/modules/auth/interfaces';
import { JwtAuthGuard } from '@shared/shared';

@ApiTags('Admin - Driver Vehicle Documents')
@UseGuards(JwtAuthGuard)
@Controller('admin/driver-vehicle-documents')
export class DriverVehicleDocumentController {
  constructor(
    private readonly driverVehicleDocumentService: DriverVehicleDocumentService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get paginated driver vehicle documents',
    description:
      'Get paginated list of driver vehicle documents with optional filters',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Document status filter',
    enum: DriverVehicleDocumentStatus,
  })
  @ApiQuery({
    name: 'driverVehicleId',
    required: false,
    description: 'Driver vehicle ID filter',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Driver vehicle documents retrieved successfully',
    type: ApiResponseDto<DriverVehicleDocumentListResponseDto>,
  })
  async getDriverVehicleDocuments(
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 10,
    @Query('status') status?: DriverVehicleDocumentStatus,
    @Query('driverVehicleId') driverVehicleId?: string,
  ) {
    const result =
      await this.driverVehicleDocumentService.getDriverVehicleDocumentsForAdmin(
        page,
        limit,
        status,
        driverVehicleId,
      );

    return {
      success: true,
      message: 'Driver vehicle documents retrieved successfully',
      data: result,
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get driver vehicle document by ID',
    description:
      'Get detailed information about a specific driver vehicle document',
  })
  @ApiParam({
    name: 'id',
    description: 'Driver vehicle document ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Driver vehicle document retrieved successfully',
    type: ApiResponseDto<DriverVehicleDocumentResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Driver vehicle document not found',
    type: ApiErrorResponseDto,
  })
  async getDriverVehicleDocumentById(@Param('id', ParseUUIDPipe) id: string) {
    const result =
      await this.driverVehicleDocumentService.getDriverVehicleDocumentByIdForAdmin(
        id,
      );

    return {
      success: true,
      message: 'Driver vehicle document retrieved successfully',
      data: result,
      timestamp: Date.now(),
    };
  }

  @Post(':id/approve-reject')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Approve or reject driver vehicle document',
    description:
      'Approve or reject a driver vehicle document. Rejection requires a note.',
  })
  @ApiParam({
    name: 'id',
    description: 'Driver vehicle document ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Document action completed successfully',
    type: ApiResponseDto<DocumentActionResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid action or missing rejection note',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Driver vehicle document not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Document already in requested status',
    type: ApiErrorResponseDto,
  })
  async approveRejectDocument(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() dto: ApproveRejectDocumentDto,
    @Req() req: ApiRequest,
  ) {
    const userProfileId = (req.user as any)?.profileId;
    let result;

    if (dto.action === DocumentAction.APPROVE) {
      result = await this.driverVehicleDocumentService.approveDocument(id, {
        createdBy: userProfileId,
      });
    } else if (dto.action === DocumentAction.REJECT) {
      if (!dto.rejectionNote || dto.rejectionNote.trim().length === 0) {
        throw new BadRequestException(
          'Rejection note is required when rejecting a document',
        );
      }
      result = await this.driverVehicleDocumentService.rejectDocument(
        id,
        dto.rejectionNote,
        { createdBy: userProfileId },
      );
    } else {
      throw new BadRequestException(
        'Invalid action. Must be APPROVE or REJECT',
      );
    }

    return {
      success: true,
      message: result.message,
      data: result,
      timestamp: Date.now(),
    };
  }
}
