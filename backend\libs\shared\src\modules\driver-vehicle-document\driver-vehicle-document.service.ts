import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { DriverVehicleRepository } from '../../repositories/driver-vehicle.repository';
import { DriverVehicleDocumentRepository } from '../../repositories/driver-vehicle-document.repository';
import { CashfreeVehicleVerificationService } from '@shared/shared/common/verifications/cashfree/cashfree-vehicle-verification.service';
import {
  DriverVehicleDocument,
  DriverVehicleDocumentStatus,
} from '@shared/shared/repositories/models/driverVehicleDocument.model';
import { VehicleDocumentRepository } from '@shared/shared/repositories/vehicle-document.repository';
import { UserOnboardingService } from '../user-onboarding/user-onboarding.service';
import { UserProfileService } from '../user-profile/user-profile.service';
import { DriverVehicleStatus } from '@shared/shared/repositories/models/driverVehicle.model';
import { UserProfileStatus } from '@shared/shared/repositories/models/userProfile.model';
import { OnboardingStep } from '@shared/shared/repositories/models/userOnboard.model';
import { FileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';
import { DriverVehicleDocumentAuditLogService } from './services/driver-vehicle-document-audit-log.service';

@Injectable()
export class DriverVehicleDocumentService {
  private readonly logger = new Logger(DriverVehicleDocumentService.name);

  constructor(
    private readonly driverVehicleRepository: DriverVehicleRepository,
    private readonly driverVehicleDocumentRepository: DriverVehicleDocumentRepository,
    private readonly cashfreeVehicleVerificationService: CashfreeVehicleVerificationService,
    private readonly vehicleDocumentRepository: VehicleDocumentRepository,
    private readonly userOnboardingService: UserOnboardingService,
    private readonly userProfileService: UserProfileService,
    private readonly fileUploadService: FileUploadService,
    private readonly driverVehicleDocumentAuditLogService: DriverVehicleDocumentAuditLogService,
  ) {}

  /**
   * Finds a driver vehicle document by driverVehicleId
   */
  async findByDriverVehicleId(
    driverVehicleId: string,
  ): Promise<DriverVehicleDocument[]> {
    return this.driverVehicleDocumentRepository.findMany({
      where: { driverVehicleId },
    });
  }
  /**
   * Verifies a driver vehicle with Cashfree and creates/updates the document if valid
   * @param driverVehicleId
   * @param options Optional parameters including createdBy
   * @returns the created/updated DriverVehicleDocument
   */
  async verifyAndSaveDocument(
    driverVehicleId: string,
    options?: { createdBy?: string },
  ): Promise<any> {
    // Find the driver vehicle
    const driverVehicle =
      await this.driverVehicleRepository.findDriverVehicleById(driverVehicleId);
    if (!driverVehicle) throw new NotFoundException('Driver vehicle not found');
    if (!driverVehicle.vehicleNumber)
      throw new ConflictException(
        'Vehicle number is required for verification',
      );

    //need to rewrite with country specific vehicle document
    const vehicleDocument =
      await this.vehicleDocumentRepository.findOneByIdentifier(
        'vehicle_registration',
      );
    if (!vehicleDocument) {
      throw new NotFoundException('Vehicle document not found');
    }
    const userProfile = await this.userProfileService.findUserProfileById(
      driverVehicle.userProfileId,
    );
    // if (!userProfile) {
    //   throw new NotFoundException(
    //     `User profile for user ID ${driverVehicle.userProfileId} not found`,
    //   );
    // }
    // Call Cashfree verification

    //delete all documents permant for this vehicle
    await this.driverVehicleDocumentRepository.hardDeleteMany({
      driverVehicleId: driverVehicle.id,
    });

    const verificationResult =
      await this.cashfreeVehicleVerificationService.verifyVehicle(
        driverVehicleId + new Date().getTime(), // Unique reference ID
        driverVehicle.vehicleNumber,
      );

    if (verificationResult.status !== 'VALID') {
      throw new ConflictException(
        'Vehicle verification failed or is not valid',
      );
    }
    let isNocRequired = false;
    const ownerName = verificationResult.owner
      ?.toLowerCase()
      .replace(/\s+/g, ''); // remove all whitespaces

    const userFullName = `${userProfile.firstName}${userProfile.lastName}`
      .toLowerCase()
      .replace(/\s+/g, ''); // remove all whitespaces

    if (ownerName && ownerName !== userFullName) {
      isNocRequired = true;

      // Update the driver vehicle to mark NOC as required
      await this.driverVehicleRepository.updateDriverVehicle(driverVehicle.id, {
        isNocRequired: true,
      });
    } else {
      await this.driverVehicleRepository.updateDriverVehicle(driverVehicle.id, {
        isNocRequired: false,
      });
    }

    // Upsert driver vehicle document
    const docData = {
      driverVehicleId: driverVehicle.id,
      details: verificationResult,
      vehicleDocumentId: vehicleDocument.id,
      status: DriverVehicleDocumentStatus.PENDING,
      createdBy: options?.createdBy || driverVehicle.userProfileId,
    };

    // Try to find existing document
    const existing = await this.driverVehicleDocumentRepository.findOne({
      where: { driverVehicleId: driverVehicle.id },
    });
    let result;
    if (existing) {
      result = await this.driverVehicleDocumentRepository.update({
        where: { id: existing.id },
        data: docData,
      });
    } else {
      result = await this.driverVehicleDocumentRepository.create(docData);

      await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
        userProfile.userId,
        userProfile.roleId,
        OnboardingStep.VEHICLE_DOCUMENTS_VERIFICATION,
      );
    }

    // Extract and save insurance information if available
    await this.createInsuranceDocument(
      driverVehicle.id,
      verificationResult,
      options?.createdBy,
    );

    // Log the document verification
    if (
      options?.createdBy &&
      options.createdBy !== driverVehicle.userProfileId &&
      driverVehicle &&
      driverVehicle.vehicleNumber
    ) {
      await this.driverVehicleDocumentAuditLogService.logDocumentVerification(
        options?.createdBy,
        driverVehicleId,
        driverVehicle.vehicleNumber,
        driverVehicle.userProfileId,
      );
    }
    return {
      isNocRequired,
      ...result,
    };
  }

  /**
   * Upload NOC document for a driver vehicle
   * @param driverVehicleId
   * @param documentUrl
   * @param options Optional parameters including createdBy
   * @returns the created/updated DriverVehicleDocument
   */
  async uploadNocDocument(
    driverVehicleId: string,
    documentUrl: string,
    options?: { createdBy?: string },
  ): Promise<DriverVehicleDocument> {
    // Find the driver vehicle
    const driverVehicle =
      await this.driverVehicleRepository.findDriverVehicleById(driverVehicleId);
    if (!driverVehicle) {
      throw new NotFoundException('Driver vehicle not found');
    }

    // Find NOC vehicle document type
    const nocDocument =
      await this.vehicleDocumentRepository.findOneByIdentifier('noc');
    if (!nocDocument) {
      throw new NotFoundException('NOC document type not found');
    }

    // Check if NOC document already exists for this driver vehicle
    const existingNocDocument =
      await this.driverVehicleDocumentRepository.findOne({
        where: {
          driverVehicleId,
          vehicleDocumentId: nocDocument.id,
        },
      });

    const docData = {
      driverVehicleId,
      vehicleDocumentId: nocDocument.id,
      documentUrl,
      status: DriverVehicleDocumentStatus.PENDING,
      details: {
        uploadedAt: new Date(),
        documentType: 'noc',
      },
      createdBy: options?.createdBy || driverVehicle.userProfileId,
    };

    let result: DriverVehicleDocument;
    if (existingNocDocument) {
      // Update existing NOC document
      result = await this.driverVehicleDocumentRepository.update({
        where: { id: existingNocDocument.id },
        data: docData,
      });
    } else {
      // Create new NOC document
      result = await this.driverVehicleDocumentRepository.create(docData);
    }

    // Log the NOC upload
    if (
      options?.createdBy &&
      options?.createdBy !== driverVehicle.userProfileId &&
      driverVehicle &&
      driverVehicle.vehicleNumber
    ) {
      this.driverVehicleDocumentAuditLogService.logNocUpload(
        options.createdBy,
        driverVehicleId,
        driverVehicle.vehicleNumber,
        driverVehicle.userProfileId,
      );
    }

    return result;
  }

  /**
   * Create or update insurance document from verification result
   * @param driverVehicleId
   * @param verificationResult
   * @param createdBy Optional user ID who created the document
   */
  private async createInsuranceDocument(
    driverVehicleId: string,
    verificationResult: any,
    createdBy?: string,
  ): Promise<void> {
    try {
      // Find insurance vehicle document type
      const insuranceDocument =
        await this.vehicleDocumentRepository.findOneByIdentifier('insurance');
      if (!insuranceDocument) {
        this.logger.warn(
          'Insurance document type not found, skipping insurance document creation',
        );
        return;
      }

      // Get the driver vehicle to access user profile ID if createdBy is not provided
      let finalCreatedBy = createdBy;
      if (!finalCreatedBy) {
        const driverVehicle =
          await this.driverVehicleRepository.findDriverVehicleById(
            driverVehicleId,
          );
        if (driverVehicle) {
          finalCreatedBy = driverVehicle.userProfileId;
        }
      }

      // If we still don't have a createdBy, skip creating the document
      if (!finalCreatedBy) {
        this.logger.warn(
          'Unable to determine createdBy for insurance document, skipping creation',
        );
        return;
      }

      // Extract insurance details from verification result
      const insuranceDetails = {
        vehicle_insurance_upto:
          verificationResult.vehicle_insurance_upto || null,
        vehicle_insurance_company_name:
          verificationResult.vehicle_insurance_company_name || null,
        vehicle_insurance_policy_number:
          verificationResult.vehicle_insurance_policy_number || null,
        extractedAt: new Date(),
        documentType: 'insurance',
      };

      // Only create insurance document if at least one insurance field is present
      if (
        insuranceDetails.vehicle_insurance_upto ||
        insuranceDetails.vehicle_insurance_company_name ||
        insuranceDetails.vehicle_insurance_policy_number
      ) {
        // Check if insurance document already exists for this driver vehicle
        const existingInsuranceDocument =
          await this.driverVehicleDocumentRepository.findOne({
            where: {
              driverVehicleId,
              vehicleDocumentId: insuranceDocument.id,
            },
          });

        const insuranceDocData = {
          driverVehicleId,
          vehicleDocumentId: insuranceDocument.id,
          status: DriverVehicleDocumentStatus.PENDING,
          details: insuranceDetails,
          documentFields: {
            insurance_upto: insuranceDetails.vehicle_insurance_upto,
            insurance_company_name:
              insuranceDetails.vehicle_insurance_company_name,
            insurance_policy_number:
              insuranceDetails.vehicle_insurance_policy_number,
          },
          createdBy: finalCreatedBy,
        };

        if (existingInsuranceDocument) {
          // Update existing insurance document
          await this.driverVehicleDocumentRepository.update({
            where: { id: existingInsuranceDocument.id },
            data: insuranceDocData,
          });
        } else {
          // Create new insurance document
          await this.driverVehicleDocumentRepository.create(insuranceDocData);
        }
      }
    } catch (error) {
      // Log error but don't fail the main verification process
      this.logger.error('Error creating insurance document:', error);
    }
  }

  /**
   * Get all vehicle documents with driver vehicle documents for a specific driver vehicle ID
   * Returns formatted data with signed URLs for document access
   */
  async getVehicleDocumentsWithDriverDocs(driverVehicleId: string) {
    //Fetch and validate driver vehicle
    const driverVehicle =
      await this.driverVehicleRepository.findDriverVehicleById(driverVehicleId);
    if (!driverVehicle) {
      throw new NotFoundException(
        `Driver vehicle with ID ${driverVehicleId} not found`,
      );
    }

    // Fetch all vehicle documents along with related driver vehicle documents
    let vehicleDocuments = await this.vehicleDocumentRepository.findMany({
      include: {
        driverVehicleDocuments: {
          where: { driverVehicleId },
          include: {
            createdByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                role: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Remove 'noc' document if not required
    if (!driverVehicle.isNocRequired) {
      vehicleDocuments = vehicleDocuments.filter(
        (doc) => doc.identifier !== 'noc',
      );
    }

    //Format response with signed URLs
    return Promise.all(
      vehicleDocuments.map(async (vehicleDoc) => {
        const relatedDocs = vehicleDoc.driverVehicleDocuments || [];
        let driverDocument = null;

        if (relatedDocs.length > 0) {
          const [firstRelatedDoc] = relatedDocs;
          let signedUrl = firstRelatedDoc.documentUrl;

          if (firstRelatedDoc.documentUrl) {
            try {
              signedUrl = await this.fileUploadService.getSignedUrl(
                firstRelatedDoc.documentUrl,
                3600, // 1 hour expiry
              );
            } catch {
              signedUrl = firstRelatedDoc.documentUrl;
            }
          }

          driverDocument = {
            ...firstRelatedDoc,
            documentUrl: signedUrl,
          };
        }

        // Exclude driverVehicleDocuments from the response
        const { driverVehicleDocuments, ...vehicleDocWithoutRelation } =
          vehicleDoc;

        return {
          ...vehicleDocWithoutRelation,
          driverDocument,
        };
      }),
    );
  }

  /**
   * Approve a driver vehicle document
   * @param id Document ID
   * @param options Optional parameters including createdBy
   */
  async approveDocument(id: string, options?: { createdBy?: string }) {
    const document = await this.driverVehicleDocumentRepository.findById(id, {
      include: {
        driverVehicle: {
          select: { id: true, vehicleNumber: true, userProfileId: true },
        },
        vehicleDocument: {
          select: { id: true, name: true },
        },
      },
    });
    if (!document) {
      throw new NotFoundException('Driver vehicle document not found');
    }

    if (document.status === DriverVehicleDocumentStatus.APPROVED) {
      throw new ConflictException('Document is already approved');
    }

    const updatedDocument =
      await this.driverVehicleDocumentRepository.updateStatus(
        id,
        DriverVehicleDocumentStatus.APPROVED,
      );

    // Log the document approval
    if (options?.createdBy) {
      this.driverVehicleDocumentAuditLogService.logDocumentApproval(
        options.createdBy,
        document,
      );
    }

    return {
      id: updatedDocument.id,
      status: updatedDocument.status,
      message: 'Document approved successfully',
      updatedAt: updatedDocument.updatedAt,
    };
  }

  /**
   * Reject a driver vehicle document
   * @param id Document ID
   * @param rejectionNote Rejection note
   * @param options Optional parameters including createdBy
   */
  async rejectDocument(
    id: string,
    rejectionNote: string,
    options?: { createdBy?: string },
  ) {
    // Get document with relations to access driverVehicle and userProfile
    const document = await this.driverVehicleDocumentRepository.findOne({
      where: { id },
      include: {
        driverVehicle: {
          select: { id: true, vehicleNumber: true, userProfileId: true },
        },
        vehicleDocument: {
          select: { id: true, name: true },
        },
      },
    });

    if (!document) {
      throw new NotFoundException('Driver vehicle document not found');
    }

    if (document.status === DriverVehicleDocumentStatus.REJECTED) {
      throw new ConflictException('Document is already rejected');
    }

    if (!rejectionNote || rejectionNote.trim().length === 0) {
      throw new ConflictException(
        'Rejection note is required when rejecting a document',
      );
    }

    // Update document status to rejected
    const updatedDocument =
      await this.driverVehicleDocumentRepository.updateStatus(
        id,
        DriverVehicleDocumentStatus.REJECTED,
        rejectionNote,
      );

    // Update driver vehicle status to pending
    if (document.driverVehicle) {
      await this.driverVehicleRepository.updateDriverVehicle(
        document.driverVehicle.id,
        { status: DriverVehicleStatus.pending },
      );
    }

    // Update user profile status to pending
    if (document.driverVehicle?.userProfileId) {
      await this.userProfileService.updateUserProfileStatus(
        document.driverVehicle.userProfileId,
        UserProfileStatus.PENDING,
      );
    }

    // Log the document rejection
    if (
      options?.createdBy &&
      document.driverVehicle &&
      document.driverVehicle.vehicleNumber
    ) {
      this.driverVehicleDocumentAuditLogService.logDocumentRejection(
        options.createdBy,
        document,
      );
    }

    return {
      id: updatedDocument.id,
      status: updatedDocument.status,
      message: 'Document rejected successfully',
      updatedAt: updatedDocument.updatedAt,
    };
  }

  /**
   * Get paginated driver vehicle documents for admin
   * @param page Page number
   * @param limit Items per page
   * @param status Optional status filter
   * @param driverVehicleId Optional driver vehicle ID filter
   */
  async getDriverVehicleDocumentsForAdmin(
    page = 1,
    limit = 10,
    status?: DriverVehicleDocumentStatus,
    driverVehicleId?: string,
  ) {
    return this.driverVehicleDocumentRepository.paginate(page, limit, {
      where: {
        ...(status && { status }),
        ...(driverVehicleId && { driverVehicleId }),
      },
      include: {
        driverVehicle: {
          include: {
            userProfile: {
              select: { id: true, firstName: true, lastName: true },
            },
            vehicleType: {
              select: { id: true, name: true },
            },
          },
        },
        vehicleDocument: {
          select: { id: true, name: true, description: true },
        },
        createdByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Get driver vehicle document by ID for admin
   * @param id Document ID
   */
  async getDriverVehicleDocumentByIdForAdmin(id: string) {
    const document = await this.driverVehicleDocumentRepository.findOne({
      where: { id },
      include: {
        driverVehicle: {
          include: {
            userProfile: {
              select: { id: true, firstName: true, lastName: true },
            },
            vehicleType: {
              select: { id: true, name: true },
            },
          },
        },
        vehicleDocument: {
          select: { id: true, name: true, description: true },
        },
        createdByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (!document) {
      throw new NotFoundException('Driver vehicle document not found');
    }

    // Generate signed URL for document if it exists
    let signedDocumentUrl = document.documentUrl;
    if (document.documentUrl) {
      try {
        signedDocumentUrl = await this.fileUploadService.getSignedUrl(
          document.documentUrl,
          3600, // 1 hour expiry
        );
      } catch (error) {
        this.logger.warn('Failed to generate signed URL for document:', error);
        signedDocumentUrl = document.documentUrl;
      }
    }

    return {
      ...document,
      documentUrl: signedDocumentUrl,
    };
  }

  /**
   * Check if all mandatory documents are approved for a vehicle
   * @param driverVehicleId Driver vehicle ID
   */
  async areAllMandatoryDocumentsApproved(
    driverVehicleId: string,
    identifiers: string[],
  ): Promise<boolean> {
    // If identifiers are provided, check only those documents
    const documents = await this.vehicleDocumentRepository.findMany({
      where: {
        identifier: { in: identifiers },
      },
    });

    if (documents.length === 0) {
      return false; // No documents found
    }

    const approvedDocuments =
      await this.driverVehicleDocumentRepository.getAllApprovedDriverDocument(
        driverVehicleId,
      );
    // Check if all documents are approved
    if (
      approvedDocuments.length === 0 ||
      documents.length !== approvedDocuments.length
    ) {
      return false; // No documents found
    }
    return true;
  }
}
