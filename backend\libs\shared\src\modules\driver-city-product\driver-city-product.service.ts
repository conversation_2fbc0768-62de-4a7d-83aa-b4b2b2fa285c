import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { DriverCityProductRepository } from '../../repositories/driver-city-product.repository';
import { CityProductRepository } from '../../repositories/city-product.repository';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { DriverVehicleRepository } from '../../repositories/driver-vehicle.repository';
import { FileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';
import { DriverCityProduct } from '../../repositories/models/driverCityProduct.model';
import { DriverVehicleStatus } from '../../repositories/models/driverVehicle.model';
import { UserProfileStatus } from '../../repositories/models/userProfile.model';
import { DriverCityProductAuditLogService } from './services/driver-city-product-audit-log.service';

@Injectable()
export class DriverCityProductService {
  constructor(
    private readonly driverCityProductRepository: DriverCityProductRepository,
    private readonly cityProductRepository: CityProductRepository,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly driverVehicleRepository: DriverVehicleRepository,
    private readonly fileUploadService: FileUploadService,
    private readonly driverCityProductAuditLogService: DriverCityProductAuditLogService,
  ) {}

  /**
   * Add city products to a driver.
   * @param driverId - Driver's user profile ID
   * @param cityProductIds - Array of city product IDs to add
   */
  async addCityProductsToDriver(
    driverId: string,
    cityProductIds: string[],
  ): Promise<DriverCityProduct[]> {
    // Validate driver exists
    const driver = await this.userProfileRepository.findById(driverId);
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // Validate city products exist and are enabled
    const cityProducts = await this.cityProductRepository.findMany({
      where: {
        id: { in: cityProductIds },
      },
    });

    if (cityProducts.length !== cityProductIds.length) {
      const foundIds = cityProducts.map((cp) => cp.id);
      const notFoundIds = cityProductIds.filter((id) => !foundIds.includes(id));
      throw new BadRequestException(
        `City products not found or disabled: ${notFoundIds.join(', ')}`,
      );
    }

    // Add city products to driver
    return this.driverCityProductRepository.addCityProductsToDriver(
      driverId,
      cityProductIds,
    );
  }

  /**
   * Remove city products from a driver.
   * @param driverId - Driver's user profile ID
   * @param cityProductIds - Array of city product IDs to remove
   */
  async removeCityProductsFromDriver(
    driverId: string,
    cityProductIds: string[],
  ): Promise<{ message: string }> {
    // Validate driver exists
    const driver = await this.userProfileRepository.findById(driverId);
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // Check if driver has these city products
    const existingRelations =
      await this.driverCityProductRepository.findDriverCityProductsByCityProductIds(
        driverId,
        cityProductIds,
      );

    if (existingRelations.length === 0) {
      throw new BadRequestException(
        'No city products found for this driver with the provided IDs',
      );
    }

    // Remove city products from driver
    await this.driverCityProductRepository.removeCityProductsFromDriver(
      driverId,
      cityProductIds,
    );

    return {
      message: `Successfully removed ${existingRelations.length} city products from driver`,
    };
  }

  /**
   * Get paginated list of city products for a driver.
   * @param driverId - Driver's user profile ID
   * @param page - Page number
   * @param limit - Items per page
   * @param productName - Product name search term
   */
  async getDriverCityProducts(
    driverId: string,
    page = 1,
    limit = 10,
    productName?: string,
  ) {
    // Validate driver exists
    const driver = await this.userProfileRepository.findById(driverId);
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // Get paginated city products for driver
    const result =
      await this.driverCityProductRepository.paginateDriverCityProducts(
        driverId,
        page,
        limit,
        productName,
      );

    // Format the response to include signed URLs for product icons

    return result;
  }

  /**
   * Get all city products for a driver (without pagination).
   * @param driverId - Driver's user profile ID
   */
  async getAllDriverCityProducts(
    driverId: string,
  ): Promise<DriverCityProduct[]> {
    // Validate driver exists
    const driver = await this.userProfileRepository.findById(driverId);
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    return this.driverCityProductRepository.findDriverCityProductsByDriverId(
      driverId,
    );
  }
  /**
   * Check if driver has specific city products.
   * @param driverId - Driver's user profile ID
   * @param cityProductIds - Array of city product IDs to check
   */
  async checkDriverHasCityProducts(
    driverId: string,
    cityProductIds: string[],
  ): Promise<{ [key: string]: boolean }> {
    const result: { [key: string]: boolean } = {};

    for (const cityProductId of cityProductIds) {
      result[cityProductId] =
        await this.driverCityProductRepository.driverCityProductExists(
          driverId,
          cityProductId,
        );
    }

    return result;
  }

  /**
   * Add city products to a driver vehicle.
   * @param driverVehicleId - Driver vehicle ID
   * @param cityProductIds - Array of city product IDs to add
   * @param options Optional parameters including createdBy
   */
  async addCityProductsToDriverVehicle(
    driverVehicleId: string,
    cityProductIds: string[],
    options?: { createdBy?: string },
  ): Promise<DriverCityProduct[]> {
    // Validate driver vehicle exists
    const driverVehicle =
      await this.driverVehicleRepository.findById(driverVehicleId);
    if (!driverVehicle) {
      throw new NotFoundException('Driver vehicle not found');
    }

    // Validate city products exist and are enabled
    const cityProducts = await this.cityProductRepository.findMany({
      where: {
        id: { in: cityProductIds },
      },
    });

    if (cityProducts.length !== cityProductIds.length) {
      const foundIds = cityProducts.map((cp) => cp.id);
      const notFoundIds = cityProductIds.filter((id) => !foundIds.includes(id));
      throw new BadRequestException(
        `City products not found or disabled: ${notFoundIds.join(', ')}`,
      );
    }

    // Add city products to driver vehicle
    const result =
      await this.driverCityProductRepository.addCityProductsToDriverVehicle(
        driverVehicle.userProfileId,
        driverVehicleId,
        cityProductIds,
      );

    // Log the addition
    if (options?.createdBy && driverVehicle.vehicleNumber) {
      this.driverCityProductAuditLogService.logAddCityProducts(
        options.createdBy,
        driverVehicleId,
        driverVehicle.vehicleNumber,
        driverVehicle.userProfileId,
      );
    }

    return result;
  }

  /**
   * Remove city products from a driver vehicle.
   * @param driverVehicleId - Driver vehicle ID
   * @param cityProductIds - Array of city product IDs to remove
   * @param options Optional parameters including createdBy
   */
  async removeCityProductsFromDriverVehicle(
    driverVehicleId: string,
    cityProductIds: string[],
    options?: { createdBy?: string },
  ): Promise<{ message: string }> {
    // Validate driver vehicle exists
    const driverVehicle =
      await this.driverVehicleRepository.findById(driverVehicleId);
    if (!driverVehicle) {
      throw new NotFoundException('Driver vehicle not found');
    }

    // Check if driver vehicle has these city products
    const existingRelations =
      await this.driverCityProductRepository.findDriverCityProductsByDriverVehicleAndCityProducts(
        driverVehicle.userProfileId,
        driverVehicleId,
        cityProductIds,
      );

    if (existingRelations.length === 0) {
      throw new BadRequestException(
        'No city products found for this driver vehicle with the provided IDs',
      );
    }

    // Remove city products from driver vehicle
    const removedCount =
      await this.driverCityProductRepository.removeCityProductsFromDriverVehicle(
        driverVehicle.userProfileId,
        driverVehicleId,
        cityProductIds,
      );

    // Check if there are any remaining city products for this vehicle
    const remainingCityProducts =
      await this.hasAnyCityProducts(driverVehicleId);

    // If no city products remain, set vehicle status to pending
    if (!remainingCityProducts) {
      await this.driverVehicleRepository.updateById(driverVehicleId, {
        status: DriverVehicleStatus.pending,
      });

      // Check if the driver has any active vehicles
      const activeVehicles = await this.driverVehicleRepository.findMany({
        where: {
          userProfileId: driverVehicle.userProfileId,
          status: DriverVehicleStatus.active,
        },
      });

      // If no active vehicles remain, set driver status to pending
      if (activeVehicles.length === 0) {
        await this.userProfileRepository.updateById(
          driverVehicle.userProfileId,
          {
            status: UserProfileStatus.PENDING,
          },
        );
      }
    }

    // Log the removal
    if (options?.createdBy && driverVehicle.vehicleNumber) {
      await this.driverCityProductAuditLogService.logRemoveCityProducts(
        options.createdBy,
        driverVehicleId,
        driverVehicle.vehicleNumber,
        driverVehicle.userProfileId,
      );
    }

    return {
      message: `Successfully removed ${removedCount} city products from driver vehicle`,
    };
  }

  /**
   * Get paginated city products for a driver vehicle.
   * @param driverVehicleId - Driver vehicle ID
   * @param page - Page number
   * @param limit - Items per page
   */
  async getDriverVehicleCityProducts(
    driverVehicleId: string,
    page = 1,
    limit = 10,
  ) {
    // Validate driver vehicle exists
    const driverVehicle =
      await this.driverVehicleRepository.findById(driverVehicleId);
    if (!driverVehicle) {
      throw new NotFoundException('Driver vehicle not found');
    }

    // Get paginated city products for driver vehicle
    const result =
      await this.driverCityProductRepository.paginateDriverVehicleCityProducts(
        driverVehicle.userProfileId,
        driverVehicleId,
        page,
        limit,
      );

    // Add signed URLs to product icons
    const dataWithSignedUrls = await Promise.all(
      result.data.map(async (driverCityProduct) => {
        if (driverCityProduct.cityProduct?.product?.icon) {
          try {
            const signedUrl = await this.fileUploadService.getSignedUrl(
              driverCityProduct.cityProduct.product.icon,
              3600, // 1 hour expiry
            );
            return {
              ...driverCityProduct,
              cityProduct: {
                ...driverCityProduct.cityProduct,
                product: {
                  ...driverCityProduct.cityProduct.product,
                  icon: signedUrl,
                },
              },
            };
          } catch (error) {
            // If signed URL generation fails, keep the original URL
            return driverCityProduct;
          }
        }
        return driverCityProduct;
      }),
    );

    return {
      ...result,
      data: dataWithSignedUrls,
    };
  }

  /**
   * Update primary status of a city product for a driver vehicle.
   * @param driverVehicleId - Driver vehicle ID
   * @param cityProductId - City product ID
   * @param isPrimary - Primary status
   */
  async updateCityProductPrimaryStatus(
    driverVehicleId: string,
    cityProductId: string,
    isPrimary: boolean,
  ): Promise<DriverCityProduct> {
    // Validate driver vehicle exists
    const driverVehicle =
      await this.driverVehicleRepository.findById(driverVehicleId);
    if (!driverVehicle) {
      throw new NotFoundException('Driver vehicle not found');
    }

    // If setting as primary, ensure only one city product is primary per driver vehicle
    if (isPrimary) {
      await this.driverCityProductRepository.updateMany(
        {
          userProfileId: driverVehicle.userProfileId,
          driverVehicleId: driverVehicleId,
        },
        {
          isPrimary: false,
        },
      );
    }

    // Update the specific city product
    return this.driverCityProductRepository.updateCityProductPrimaryStatus(
      driverVehicle.userProfileId,
      driverVehicleId,
      cityProductId,
      isPrimary,
    );
  }

  /**
   * Check if driver vehicle has at least one city product assigned.
   * @param driverVehicleId - Driver vehicle ID
   */
  async hasAnyCityProducts(driverVehicleId: string): Promise<boolean> {
    const driverVehicle =
      await this.driverVehicleRepository.findById(driverVehicleId);
    if (!driverVehicle) {
      return false;
    }

    const count = await this.driverCityProductRepository.count({
      userProfileId: driverVehicle.userProfileId,
      driverVehicleId: driverVehicleId,
    });

    return count > 0;
  }
}
